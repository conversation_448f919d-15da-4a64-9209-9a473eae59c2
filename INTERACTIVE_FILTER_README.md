# Interactive Database Filter Manager

A beautiful, rich CLI interface for managing court database filtering rules with real-time preview and custom rule creation.

## Features

### 🎨 Rich CLI Interface
- Beautiful terminal interface with colors, tables, and panels
- Interactive menus with keyboard navigation
- Real-time status updates and progress indicators
- Responsive layout that adapts to terminal size

### 📋 Rule Management
- **View Current Rules**: Display all filtering rules in a formatted table
- **Add New Rules**: Interactive wizard for creating custom filter rules
- **Toggle Rules**: Enable/disable rules without deleting them
- **Delete Rules**: Remove unwanted rules with confirmation
- **Save/Load Rules**: Export and import rule configurations as JSON files

### 🔍 Filter Operations
- **Equals**: Exact field matching
- **Contains**: Substring matching (case-sensitive or case-insensitive)
- **Starts With**: Prefix matching
- **Ends With**: Suffix matching

### 📊 Impact Analysis
- **Preview Mode**: See how many entries each rule would affect
- **Database Statistics**: View current database state
- **Real-time Counts**: See percentage impact of each rule

### 🛡️ Safety Features
- **Preview Before Apply**: See exactly what will be removed
- **Confirmation Prompts**: Multiple confirmations for destructive operations
- **Detailed Logging**: Track what was removed and why
- **Rule Validation**: Ensure only valid database fields are used

## Usage

### Launch Interactive CLI

```bash
# Launch with default database
python -m courtrss.rss_feed --interactive_filter

# Launch with custom database path
python -m courtrss.rss_feed --interactive_filter --db_path /path/to/database.db

# Or run directly
python courtrss/interactive_filter.py --db_path /path/to/database.db
```

### Menu Options

1. **View Current Rules** - Display all filtering rules in a table
2. **Add New Rule** - Interactive rule creation wizard
3. **Toggle Rule Active/Inactive** - Enable/disable rules
4. **Delete Rule** - Remove rules with confirmation
5. **Preview Filter Impact** - See how many entries would be affected
6. **Apply Filters to Database** - Execute the filtering (irreversible!)
7. **Save Rules to File** - Export rules as JSON
8. **Load Rules from File** - Import rules from JSON
9. **Show Database Info** - Display database statistics
10. **Exit** - Quit the application

## Rule Types

### Pre-loaded Rules
The system comes with existing hardcoded rules that match the current filtering logic:

- Remove entries with no link text
- Keep only Complaint and Amended Complaint entries
- Remove criminal cases (USA v., United States of America v.)
- Remove Social Security cases
- Remove County Prosecutor cases
- Remove placeholder entries
- Remove magistrate judge cases

### Custom Rules
You can create custom rules with these operators:

#### Field Selection
Choose from any database field:
- `id`, `court_code`, `docket_number`, `case_title`, `title`
- `summary`, `link_text`, `link_url`, `published_date`, `entry_id`
- `feed_url`, `created_at`

#### Operators
- **equals**: Exact match (`field = value`)
- **contains**: Substring match (`field LIKE %value%`)
- **starts_with**: Prefix match (`field LIKE value%`)
- **ends_with**: Suffix match (`field LIKE %value`)

#### Case Sensitivity
For string operations (contains, starts_with, ends_with), you can choose:
- Case-sensitive matching
- Case-insensitive matching (default)

## Examples

### Example 1: Remove Specific Court
```
Field: court_code
Operator: equals
Value: njd
Description: Remove all New Jersey District entries
```

### Example 2: Remove Cases with Specific Keywords
```
Field: case_title
Operator: contains
Value: insurance
Case Sensitive: No
Description: Remove insurance-related cases
```

### Example 3: Remove Old Entries
```
Field: created_at
Operator: starts_with
Value: 2023
Description: Remove entries from 2023
```

## Rule Configuration Files

### Save Rules
Rules can be saved to JSON files for backup or sharing:

```json
[
  {
    "field": "case_title",
    "operator": "contains",
    "value": "insurance",
    "description": "Remove insurance cases",
    "case_sensitive": false,
    "active": true
  }
]
```

### Load Rules
- Load rules from JSON files
- Choose to replace existing rules or append to them
- Validate rule format and field names

## Safety and Best Practices

### Before Applying Filters
1. **Always use Preview Mode** to see impact
2. **Start with inactive rules** to test them
3. **Save your rule configuration** before applying
4. **Backup your database** before major filtering operations

### Database Backup
```bash
# Create backup before filtering
cp .sqlite/court_entries.db .sqlite/court_entries_backup.db
```

### Testing Rules
1. Create rules as inactive first
2. Use Preview Mode to see impact
3. Activate rules one by one
4. Test with small datasets first

## Advanced Features

### Batch Operations
- Apply multiple rules in sequence
- See cumulative impact across all rules
- Individual rule impact reporting

### Rule Validation
- Field name validation against database schema
- SQL injection prevention
- Error handling for malformed rules

### Performance
- Efficient SQL query generation
- Progress indicators for long operations
- Optimized for large databases

## Troubleshooting

### Common Issues

**Rich library not found**
```bash
pip install rich
```

**Database not found**
- Ensure database path is correct
- Run court monitoring first to create database
- Check file permissions

**Rule not working as expected**
- Use Preview Mode to debug
- Check field names and values
- Verify case sensitivity settings

### Error Messages
- Clear error messages with suggested fixes
- Validation errors show valid options
- Database errors include SQL details

## Integration

### With Existing System
The interactive filter integrates seamlessly with the existing court monitoring system:

- Uses same database schema
- Respects existing filtering logic
- Can be used alongside command-line tools

### Automation
Rules can be saved and loaded for automated filtering:

```bash
# Save current rules
python -m courtrss.rss_feed --interactive_filter
# (use menu option 7 to save)

# Apply saved rules programmatically
# (future feature - batch rule application)
```

## Screenshots

The interface includes:
- 🎨 Colorful tables with proper alignment
- 📊 Progress bars and status indicators
- ⚠️ Warning panels for destructive operations
- ✅ Success confirmations with details
- 📋 Formatted rule listings with descriptions

## Future Enhancements

- Rule templates for common filtering patterns
- Regex support for advanced pattern matching
- Rule scheduling and automation
- Export filtered data to different formats
- Integration with external rule repositories