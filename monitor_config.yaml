# Runtime configuration for CourtRSS keyword monitoring mode
# Use this with: python -m courtrss.rss_feed --config monitor_config.yaml

# RSS feeds to monitor (examples from major district courts)
rss_urls:
  - https://ecf.cacd.uscourts.gov/cgi-bin/rss_outside.pl  # Central District of California
  - https://ecf.nysd.uscourts.gov/cgi-bin/rss_outside.pl  # Southern District of New York
  - https://ecf.ilnd.uscourts.gov/cgi-bin/rss_outside.pl  # Northern District of Illinois
  - https://ecf.txnd.uscourts.gov/cgi-bin/rss_outside.pl  # Northern District of Texas
  - https://ecf.flsd.uscourts.gov/cgi-bin/rss_outside.pl  # Southern District of Florida

# Keywords to search for in case titles and summaries
keywords:
  # Technology companies
  - "Apple"
  - "Google"
  - "Microsoft"
  - "Amazon"
  - "Meta"
  - "Facebook"
  - "Tesla"
  
  # Legal terms
  - "patent"
  - "trademark"
  - "copyright"
  - "antitrust"
  - "class action"
  
  # Financial terms
  - "securities"
  - "fraud"
  - "breach of contract"

# How often to check feeds (in seconds)
interval: 300  # Check every 5 minutes

# Retry configuration
retries: 3  # Try 3 times if a feed fails
retry_interval: 60  # Wait 60 seconds between retries

# Notification methods
notifications:
  # Desktop notification (full screen green window)
  - type: window_notification
  
  # Discord webhook (uncomment and add your webhook URL to use)
  # - type: discord_webhook
  #   webhook_url: https://discord.com/api/webhooks/YOUR_WEBHOOK_URL_HERE