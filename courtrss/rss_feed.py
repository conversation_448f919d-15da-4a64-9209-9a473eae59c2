import math
import feedparser
import time
from threading import Thread, Lock
import re
import webbrowser
import argparse
import yaml
import sys
import requests
import logging
import sqlite3
import os
from datetime import datetime
from urllib.parse import urljoin

# Make tkinter optional for database-only mode
try:
    import tkinter as tk
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    print("Warning: tkinter not available. Window notifications will be disabled.")

logging.basicConfig(level=logging.INFO)

already_notified = set()
lock = Lock()

def extract_link_from_summary(summary):
    link_text_match = re.search(r'\[([^\]]+)\]', summary)
    link_text = link_text_match.group(1) if link_text_match else None

    url_match = re.search(r'<a\s+href=["\'](http[^\s"\'<>]+)["\']', summary, re.IGNORECASE)
    url = url_match.group(1) if url_match else None

    return link_text, url

def parse_docket_and_title(title):
    """
    Parse docket number and case title from entry title.
    Docket format: N:YY-XX-NNNNN (exactly 13 characters)
    Example: "2:25-mj-00329-1 USA v. Portal-Ruiz" -> ("2:25-mj-00329", "USA v. Portal-Ruiz")
    """
    title = title.strip()
    
    # Look for docket pattern: digit:2digits-2chars-5digits with optional suffix
    import re
    docket_pattern = r'^(\d:\d{2}-[a-zA-Z]{2}-\d{5})(?:-[A-Z0-9\-]+)?\s+(.*)$'
    match = re.match(docket_pattern, title)
    
    if match:
        # Extract ONLY the 13-character docket number
        docket_number = match.group(1)
        # Extract case title (everything after the docket and any suffix)
        case_title = match.group(2).strip()
        return docket_number, case_title
    
    # If no match, return None for docket and full title
    return None, title.strip()

def init_database(db_path=None):
    """Initialize SQLite database with court entries table."""
    if db_path is None:
        # Create .sqlite directory if it doesn't exist
        sqlite_dir = ".sqlite"
        os.makedirs(sqlite_dir, exist_ok=True)
        db_path = os.path.join(sqlite_dir, "court_entries.db")
    else:
        # If a custom path is provided, ensure the directory exists
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS court_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            court_code TEXT NOT NULL,
            docket_number TEXT,
            case_title TEXT,
            title TEXT NOT NULL,
            summary TEXT,
            link_text TEXT,
            link_url TEXT,
            published_date TEXT,
            entry_id TEXT,
            feed_url TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(court_code, entry_id, link_url)
        )
    ''')
    
    # Add new columns if they don't exist (for existing databases)
    try:
        cursor.execute('ALTER TABLE court_entries ADD COLUMN docket_number TEXT')
        logging.info("Added docket_number column to existing table")
    except sqlite3.OperationalError:
        pass  # Column already exists
    
    try:
        cursor.execute('ALTER TABLE court_entries ADD COLUMN case_title TEXT')
        logging.info("Added case_title column to existing table")
    except sqlite3.OperationalError:
        pass  # Column already exists
    
    # Create index for faster queries
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_court_code ON court_entries(court_code)
    ''')
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_docket_number ON court_entries(docket_number)
    ''')
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_published_date ON court_entries(published_date)
    ''')
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_created_at ON court_entries(created_at)
    ''')
    
    # Initialize blocked phrases table
    init_blocked_phrases_table(conn, cursor)
    
    conn.commit()
    conn.close()
    logging.info(f"Database initialized at {db_path}")
    return db_path

def init_blocked_phrases_table(conn, cursor):
    """Initialize the blocked phrases table."""
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS blocked_phrases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            phrase TEXT NOT NULL,
            field TEXT NOT NULL,
            word_boundary BOOLEAN DEFAULT 1,
            case_sensitive BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(phrase, field)
        )
    ''')
    
    # Create index for faster queries
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_blocked_phrase ON blocked_phrases(phrase)
    ''')
    cursor.execute('''
        CREATE INDEX IF NOT EXISTS idx_blocked_field ON blocked_phrases(field)
    ''')

def add_blocked_phrase(db_path, phrase, field='case_title', word_boundary=True, case_sensitive=False):
    """Add a blocked phrase to the database and remove existing matches."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Ensure blocked phrases table exists
    init_blocked_phrases_table(conn, cursor)
    
    try:
        # Add the blocked phrase
        cursor.execute('''
            INSERT OR REPLACE INTO blocked_phrases (phrase, field, word_boundary, case_sensitive)
            VALUES (?, ?, ?, ?)
        ''', (phrase, field, word_boundary, case_sensitive))
        
        if cursor.rowcount > 0:
            logging.info(f"Added blocked phrase: '{phrase}' for field '{field}'")
            
            # Remove existing entries that match this phrase
            deleted_count = remove_existing_by_phrase(conn, cursor, phrase, field, word_boundary, case_sensitive)
            
            conn.commit()
            return True, deleted_count
        
    except sqlite3.Error as e:
        logging.error(f"Error adding blocked phrase: {e}")
        return False, 0
    finally:
        conn.close()
    
    return False, 0

def remove_blocked_phrase(db_path, phrase, field='case_title'):
    """Remove a blocked phrase from the database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            DELETE FROM blocked_phrases WHERE phrase = ? AND field = ?
        ''', (phrase, field))
        
        if cursor.rowcount > 0:
            conn.commit()
            logging.info(f"Removed blocked phrase: '{phrase}' for field '{field}'")
            return True
        else:
            logging.info(f"Blocked phrase not found: '{phrase}' for field '{field}'")
            return False
        
    except sqlite3.Error as e:
        logging.error(f"Error removing blocked phrase: {e}")
        return False
    finally:
        conn.close()

def get_blocked_phrases(db_path):
    """Get all blocked phrases from the database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT phrase, field, word_boundary, case_sensitive, created_at 
            FROM blocked_phrases 
            ORDER BY created_at DESC
        ''')
        
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        # Convert to list of dictionaries
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))
        
        return results
        
    except sqlite3.Error as e:
        logging.error(f"Error getting blocked phrases: {e}")
        return []
    finally:
        conn.close()

def check_blocked_phrases(db_path, entry_data):
    """Check if an entry matches any blocked phrases."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Ensure blocked phrases table exists
    init_blocked_phrases_table(conn, cursor)
    
    try:
        cursor.execute('''
            SELECT phrase, field, word_boundary, case_sensitive 
            FROM blocked_phrases
        ''')
        
        blocked_phrases = cursor.fetchall()
        logging.debug(f"Found {len(blocked_phrases)} blocked phrases")
        
        for phrase, field, word_boundary, case_sensitive in blocked_phrases:
            # Get the field value from entry_data
            field_value = entry_data.get(field, '')
            logging.debug(f"Checking phrase '{phrase}' in field '{field}': '{field_value}'")
            if not field_value:
                continue
            
            # Check if the phrase matches
            if word_boundary:
                # Use word boundary regex with lookaround to handle special characters
                import re
                escaped_phrase = re.escape(phrase)
                pattern = r'(?<!\w)' + escaped_phrase + r'(?!\w)'
                
                if case_sensitive:
                    if re.search(pattern, field_value):
                        logging.debug(f"MATCHED (case sensitive): '{phrase}' in '{field_value}'")
                        return True
                else:
                    if re.search(pattern, field_value, re.IGNORECASE):
                        logging.debug(f"MATCHED (case insensitive): '{phrase}' in '{field_value}'")
                        return True
            else:
                # Simple substring match
                if case_sensitive:
                    if phrase in field_value:
                        return True
                else:
                    if phrase.lower() in field_value.lower():
                        return True
        
        return False
        
    except sqlite3.Error as e:
        logging.error(f"Error checking blocked phrases: {e}")
        return False
    finally:
        conn.close()

def remove_existing_by_phrase(conn, cursor, phrase, field, word_boundary, case_sensitive):
    """Remove existing entries that match a blocked phrase."""
    try:
        if word_boundary:
            # Use word boundary matching
            if case_sensitive:
                # SQLite doesn't have native regex, so we'll use GLOB for word boundaries
                # This is a simplified approach - for exact word boundary matching,
                # we'd need to fetch and check each row
                cursor.execute(f'''
                    SELECT id, {field} FROM court_entries 
                    WHERE {field} IS NOT NULL
                ''')
                
                import re
                escaped_phrase = re.escape(phrase)
                pattern = r'(?<!\w)' + escaped_phrase + r'(?!\w)'
                ids_to_delete = []
                
                for row_id, field_value in cursor.fetchall():
                    if re.search(pattern, field_value):
                        ids_to_delete.append(row_id)
                
                if ids_to_delete:
                    placeholders = ','.join('?' * len(ids_to_delete))
                    cursor.execute(f'DELETE FROM court_entries WHERE id IN ({placeholders})', ids_to_delete)
                    return cursor.rowcount
            else:
                # Case-insensitive word boundary matching
                cursor.execute(f'''
                    SELECT id, {field} FROM court_entries 
                    WHERE {field} IS NOT NULL
                ''')
                
                import re
                escaped_phrase = re.escape(phrase)
                pattern = r'(?<!\w)' + escaped_phrase + r'(?!\w)'
                ids_to_delete = []
                
                for row_id, field_value in cursor.fetchall():
                    if re.search(pattern, field_value, re.IGNORECASE):
                        ids_to_delete.append(row_id)
                
                if ids_to_delete:
                    placeholders = ','.join('?' * len(ids_to_delete))
                    cursor.execute(f'DELETE FROM court_entries WHERE id IN ({placeholders})', ids_to_delete)
                    return cursor.rowcount
        else:
            # Simple substring matching
            if case_sensitive:
                cursor.execute(f'''
                    DELETE FROM court_entries 
                    WHERE {field} LIKE ?
                ''', (f'%{phrase}%',))
            else:
                cursor.execute(f'''
                    DELETE FROM court_entries 
                    WHERE LOWER({field}) LIKE LOWER(?)
                ''', (f'%{phrase}%',))
            
            return cursor.rowcount
        
        return 0
        
    except sqlite3.Error as e:
        logging.error(f"Error removing existing entries by phrase: {e}")
        return 0

def save_court_entry(db_path, court_code, entry, feed_url):
    """Save a court entry to the database only if link text is exactly 'Complaint' or 'Complaint, Amended'."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Extract link information
    link_text, link_url = extract_link_from_summary(entry.summary)
    
    # Only save if link_text is exactly 'Complaint' or 'Complaint, Amended'
    if not link_text or link_text not in ['Complaint', 'Complaint, Amended']:
        conn.close()
        return False
    
    # Parse docket number and case title from entry title
    docket_number, case_title = parse_docket_and_title(entry.title)
    
    # Check blocked phrases
    entry_data = {
        'title': entry.title,
        'case_title': case_title,
        'docket_number': docket_number,
        'summary': entry.summary,
        'link_text': link_text
    }
    
    if check_blocked_phrases(db_path, entry_data):
        logging.debug(f"Entry blocked by phrase filter: {entry.title}")
        conn.close()
        return False
    
    # Filter out cases with "USA v." or "United States of America v." in either the full title or parsed case title
    if entry.title and ("USA v." in entry.title or "United States of America v." in entry.title):
        conn.close()
        return False
    
    if case_title and (case_title.startswith("USA v.") or case_title.startswith("United States of America v.")):
        conn.close()
        return False
    
    # Filter out "Commissioner of Social Security" cases (case insensitive)
    if case_title and "commissioner of social security" in case_title.lower():
        conn.close()
        return False
    
    # Filter out "Commissioner of the Social Security Administration" cases (case insensitive)
    if case_title and "commissioner of the social security administration" in case_title.lower():
        conn.close()
        return False
    
    # Filter out "County Prosecutor" cases (case insensitive)
    if case_title and "county prosecutor" in case_title.lower():
        conn.close()
        return False
    
    # Filter out any Social Security Administration cases (case insensitive)
    if case_title and "social security administration" in case_title.lower():
        conn.close()
        return False
    
    # Filter out cases containing both "sheriff" and "office" (case insensitive)
    if case_title:
        case_title_lower = case_title.lower()
        if "sheriff" in case_title_lower and "office" in case_title_lower:
            conn.close()
            return False
    
    # Filter out generic placeholder entries
    if entry.title and entry.title.strip() == "1:99-mc-09999 Plaintiff(s) v. Defendant(s)":
        conn.close()
        return False
    
    # Filter out magistrate judge cases (-mj-)
    if docket_number and "-mj-" in docket_number:
        conn.close()
        return False
    
    # Filter out placeholder docket number
    if docket_number == "1:99-mc-09999":
        conn.close()
        return False
    
    
    # Get published date
    published_date = None
    if hasattr(entry, 'published'):
        published_date = entry.published
    elif hasattr(entry, 'updated'):
        published_date = entry.updated
    
    # Get entry ID
    entry_id = getattr(entry, 'id', None) or getattr(entry, 'guid', None)
    
    try:
        cursor.execute('''
            INSERT OR IGNORE INTO court_entries 
            (court_code, docket_number, case_title, title, summary, link_text, link_url, published_date, entry_id, feed_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            court_code,
            docket_number,
            case_title,
            entry.title,
            entry.summary,
            link_text,
            link_url,
            published_date,
            entry_id,
            feed_url
        ))
        
        if cursor.rowcount > 0:
            docket_info = f" [{docket_number}]" if docket_number else ""
            logging.info(f"Saved complaint entry for {court_code}{docket_info}: {case_title} - {link_text}")
            return True
        
    except sqlite3.Error as e:
        logging.error(f"Error saving entry to database: {e}")
    finally:
        conn.commit()
        conn.close()
    
    return False

def generate_court_rss_urls(court_codes):
    """Generate RSS URLs for the given court codes."""
    base_url = "https://ecf.{}.uscourts.gov/cgi-bin/rss_outside.pl"
    return [base_url.format(code) for code in court_codes]

def query_court_entries(db_path, court_code=None, limit=None, order_by="created_at DESC"):
    """Query court entries from the database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    query = "SELECT * FROM court_entries"
    params = []
    
    if court_code:
        query += " WHERE court_code = ?"
        params.append(court_code)
    
    query += f" ORDER BY {order_by}"
    
    if limit:
        query += " LIMIT ?"
        params.append(limit)
    
    try:
        cursor.execute(query, params)
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        # Convert to list of dictionaries
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))
        
        return results
    except sqlite3.Error as e:
        logging.error(f"Error querying database: {e}")
        return []
    finally:
        conn.close()

def migrate_existing_entries(db_path, force=False):
    """Migrate existing entries to populate docket_number and case_title fields."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Add new columns if they don't exist
        try:
            cursor.execute('ALTER TABLE court_entries ADD COLUMN docket_number TEXT')
            logging.info("Added docket_number column to existing table")
        except sqlite3.OperationalError:
            pass  # Column already exists
        
        try:
            cursor.execute('ALTER TABLE court_entries ADD COLUMN case_title TEXT')
            logging.info("Added case_title column to existing table")
        except sqlite3.OperationalError:
            pass  # Column already exists
        
        # Get ALL entries to re-parse them properly
        cursor.execute("SELECT id, title FROM court_entries")
        entries_to_migrate = cursor.fetchall()
        
        migrated_count = 0
        for entry_id, title in entries_to_migrate:
            if title:  # Only process if title exists
                docket_number, case_title = parse_docket_and_title(title)
                
                cursor.execute("""
                    UPDATE court_entries 
                    SET docket_number = ?, case_title = ?
                    WHERE id = ?
                """, (docket_number, case_title, entry_id))
                
                migrated_count += 1
        
        conn.commit()
        logging.info(f"Database migration: {migrated_count} entries updated with docket numbers and case titles")
        return migrated_count
        
    except sqlite3.Error as e:
        logging.error(f"Error migrating database: {e}")
        return 0
    finally:
        conn.close()

def clean_non_complaint_entries(db_path):
    """Remove existing entries based on multiple filter criteria."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Count entries before cleanup
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_before = cursor.fetchone()[0]
        
        # Delete entries based on multiple criteria
        cursor.execute("""
            DELETE FROM court_entries 
            WHERE link_text IS NULL 
            OR link_text NOT IN ('Complaint', 'Complaint, Amended')
            OR title LIKE '%USA v.%'
            OR title LIKE '%United States of America v.%'
            OR case_title LIKE 'USA v.%'
            OR case_title LIKE 'United States of America v.%'
            OR LOWER(case_title) LIKE '%commissioner of social security%'
            OR LOWER(title) LIKE '%commissioner of social security%'
            OR LOWER(case_title) LIKE '%commissioner of the social security administration%'
            OR LOWER(title) LIKE '%commissioner of the social security administration%'
            OR LOWER(case_title) LIKE '%county prosecutor%'
            OR LOWER(title) LIKE '%county prosecutor%'
            OR LOWER(case_title) LIKE '%social security administration%'
            OR LOWER(title) LIKE '%social security administration%'
            OR (LOWER(case_title) LIKE '%sheriff%' AND LOWER(case_title) LIKE '%office%')
            OR (LOWER(title) LIKE '%sheriff%' AND LOWER(title) LIKE '%office%')
            OR title = '1:99-mc-09999 Plaintiff(s) v. Defendant(s)'
            OR docket_number = '1:99-mc-09999'
            OR case_title GLOB '-[0-9]*'
            OR LENGTH(docket_number) > 13
            OR docket_number LIKE '%-mj-%'
            OR title LIKE '%-mj-%'
        """)
        
        deleted_count = cursor.rowcount
        conn.commit()
        
        # Count entries after cleanup
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_after = cursor.fetchone()[0]
        
        logging.info(f"Database cleanup: {deleted_count} filtered entries removed ({total_before} -> {total_after})")
        return deleted_count
        
    except sqlite3.Error as e:
        logging.error(f"Error cleaning database: {e}")
        return 0
    finally:
        conn.close()

def remove_duplicate_entries(db_path):
    """Remove duplicate entries keeping only the oldest one for each unique case."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Count entries before cleanup
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_before = cursor.fetchone()[0]
        
        # Delete duplicates, keeping the one with the smallest id (oldest)
        cursor.execute("""
            DELETE FROM court_entries 
            WHERE id NOT IN (
                SELECT MIN(id) 
                FROM court_entries 
                GROUP BY court_code, docket_number, link_text
            )
        """)
        
        deleted_count = cursor.rowcount
        conn.commit()
        
        # Count entries after cleanup
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_after = cursor.fetchone()[0]
        
        logging.info(f"Duplicate removal: {deleted_count} duplicate entries removed ({total_before} -> {total_after})")
        return deleted_count
        
    except sqlite3.Error as e:
        logging.error(f"Error removing duplicates: {e}")
        return 0
    finally:
        conn.close()

def remove_entries_by_field(db_path, field_name, field_value):
    """Remove entries where a specific field matches a specific value."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Valid field names for security
    valid_fields = [
        'id', 'court_code', 'docket_number', 'case_title', 'title', 
        'summary', 'link_text', 'link_url', 'published_date', 'entry_id', 
        'feed_url', 'created_at'
    ]
    
    if field_name not in valid_fields:
        logging.error(f"Invalid field name: {field_name}. Valid fields: {', '.join(valid_fields)}")
        conn.close()
        return 0
    
    try:
        # Count entries before removal
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_before = cursor.fetchone()[0]
        
        # Count entries that match the criteria
        cursor.execute(f"SELECT COUNT(*) FROM court_entries WHERE {field_name} = ?", (field_value,))
        matching_count = cursor.fetchone()[0]
        
        if matching_count == 0:
            logging.info(f"No entries found with {field_name} = '{field_value}'")
            conn.close()
            return 0
        
        # Show matching entries before deletion
        cursor.execute(f"SELECT id, court_code, docket_number, case_title FROM court_entries WHERE {field_name} = ?", (field_value,))
        matching_entries = cursor.fetchall()
        
        logging.info(f"Found {matching_count} entries with {field_name} = '{field_value}':")
        for entry in matching_entries:
            entry_id, court_code, docket_number, case_title = entry
            docket_info = f" [{docket_number}]" if docket_number else ""
            logging.info(f"  ID {entry_id}: {court_code}{docket_info} - {case_title}")
        
        # Delete entries that match the criteria
        cursor.execute(f"DELETE FROM court_entries WHERE {field_name} = ?", (field_value,))
        deleted_count = cursor.rowcount
        conn.commit()
        
        # Count entries after removal
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_after = cursor.fetchone()[0]
        
        logging.info(f"Removed {deleted_count} entries with {field_name} = '{field_value}' ({total_before} -> {total_after})")
        return deleted_count
        
    except sqlite3.Error as e:
        logging.error(f"Error removing entries by field: {e}")
        return 0
    finally:
        conn.close()

def get_database_stats(db_path):
    """Get statistics about the database."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Total entries
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_entries = cursor.fetchone()[0]
        
        # Complaint entries
        cursor.execute("""
            SELECT COUNT(*) FROM court_entries 
            WHERE link_text IN ('Complaint', 'Complaint, Amended')
        """)
        complaint_entries = cursor.fetchone()[0]
        
        # Entries by court
        cursor.execute("""
            SELECT court_code, COUNT(*) as count 
            FROM court_entries 
            GROUP BY court_code 
            ORDER BY count DESC
        """)
        court_stats = cursor.fetchall()
        
        # Latest entry
        cursor.execute("""
            SELECT court_code, title, created_at 
            FROM court_entries 
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        latest_entry = cursor.fetchone()
        
        return {
            'total_entries': total_entries,
            'complaint_entries': complaint_entries,
            'court_stats': court_stats,
            'latest_entry': latest_entry
        }
    except sqlite3.Error as e:
        logging.error(f"Error getting database stats: {e}")
        return None
    finally:
        conn.close()

def check_feeds_and_save_to_db(court_codes, db_path, retries=3, retry_interval=60):
    """Check court RSS feeds and save all entries to database."""
    rss_urls = generate_court_rss_urls(court_codes)
    retry_count = {url: 0 for url in rss_urls}
    
    for i, url in enumerate(rss_urls):
        court_code = court_codes[i]
        success = False
        
        while retry_count[url] < retries:
            try:
                feed = feedparser.parse(url)
                if feed.bozo and hasattr(feed, 'status') and (400 <= feed.status < 600):
                    retry_count[url] += 1
                    logging.warning(f"Error {feed.status} for {court_code} ({url}), retrying {retry_count[url]}/{retries}...")
                    time.sleep(retry_interval)
                else:
                    success = True
                    break
            except Exception as e:
                retry_count[url] += 1
                logging.warning(f"Exception for {court_code} ({url}): {e}, retrying {retry_count[url]}/{retries}...")
                time.sleep(retry_interval)

        if not success:
            logging.error(f"Failed to fetch feed from {court_code} ({url}) after {retries} retries")
            continue

        # Save complaint entries to database
        entries_processed = 0
        complaints_saved = 0
        for entry in feed.entries:
            entries_processed += 1
            if save_court_entry(db_path, court_code, entry, url):
                complaints_saved += 1
        
        if entries_processed > 0:
            logging.info(f"Processed {entries_processed} entries for {court_code}, saved {complaints_saved} complaints")

def check_feeds(rss_urls, keywords, notify_methods, retries=3, retry_interval=60):
    retry_count = {url: 0 for url in rss_urls}

    for url in rss_urls:
        success = False
        while retry_count[url] < retries:
            feed = feedparser.parse(url)
            if feed.bozo and (400 <= feed.status < 600):
                retry_count[url] += 1
                logging.warning(f"Error {feed.status} for {url}, retrying {retry_count[url]}/{retries}...")
                time.sleep(retry_interval)
            else:
                success = True
                break

        if not success:
            notify_error(f"Failed to fetch feed from {url} after {retries} retries", notify_methods)
            continue

        for entry in feed.entries:
            title = entry.title.lower()
            summary = entry.summary.lower()
            for keyword in keywords:
                if keyword.lower() in title or keyword.lower() in summary:
                    link_text, link_url = extract_link_from_summary(entry.summary)
                    if link_url in already_notified:
                        continue
                    with lock:
                        already_notified.add(link_url)
                    notify(entry.title, link_text, link_url, notify_methods)

def notify(title, link_text, link_url, methods):
    for method in methods:
        if method['type'] == 'window_notification':
            Thread(target=show_green_screen, args=(title, link_text, link_url)).start()
        elif method['type'] == 'discord_webhook':
            send_discord_notification(title, link_text, link_url, method['webhook_url'])

def notify_error(message, methods):
    for method in methods:
        if method['type'] == 'window_notification':
            Thread(target=show_green_screen, args=(message, None, None)).start()
        elif method['type'] == 'discord_webhook':
            send_discord_notification(message, None, None, method['webhook_url'])

def show_green_screen(title_text, link_text, link_url):
    if not TKINTER_AVAILABLE:
        print(f"NOTIFICATION: {title_text}")
        if link_text and link_url:
            print(f"Link: {link_text} - {link_url}")
        return
    
    root = tk.Tk()

    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    root.geometry(f"{screen_width}x{screen_height}+0+0")
    root.configure(background='green')

    title_label = tk.Label(root, text=title_text, font=("Helvetica", 40, "bold"), bg="green", fg="white")
    title_label.pack(pady=20)

    if link_text and link_url:
        link_label = tk.Label(root, text=link_text, font=("Helvetica", 25, "underline"), fg="blue", bg="green", cursor="hand2")
        link_label.pack(pady=20)
        link_label.bind("<Button-1>", lambda e: webbrowser.open_new(link_url))

    root.bind("<Escape>", lambda event: root.destroy())
    root.mainloop()

def send_discord_notification(title, link_text, link_url, webhook_url):
    content = f"**{title}**"
    if link_url:
        content += f"\n[Link]({link_url})"
    data = {"content": content}
    try:
        response = requests.post(webhook_url, json=data)
        if response.status_code != 204:
            logging.error(f"Failed to send Discord notification: {response.status_code}")
    except Exception as e:
        logging.error(f"Error sending Discord notification: {e}")

def start_monitoring(rss_urls, keywords, interval, notify_methods, retries, retry_interval):
    while True:
        check_feeds(rss_urls, keywords, notify_methods, retries, retry_interval)
        time.sleep(interval)

def start_court_monitoring(court_codes, db_path, interval, retries, retry_interval):
    """Start monitoring court feeds and saving to database."""
    while True:
        check_feeds_and_save_to_db(court_codes, db_path, retries, retry_interval)
        time.sleep(interval)

def parse_yaml_config(config_path):
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
        return config['rss_urls'], config['keywords'], config['notifications'], config.get('interval', 60), config.get('retries', 3), config.get('retry_interval', 60)

def main():
    parser = argparse.ArgumentParser(description="Monitor RSS feeds for specific keywords or save court data to database.")
    parser.add_argument('--rss_urls', help="Comma-separated list of RSS feed URLs.")
    parser.add_argument('--keywords', help="Comma-separated list of keywords.")
    parser.add_argument('--config', help="Path to YAML configuration file.")
    parser.add_argument('--interval', type=int, default=60, help="Time interval between checks (in seconds).")
    parser.add_argument('--retries', type=int, default=3, help="Number of retries if RSS feed fetch fails.")
    parser.add_argument('--retry_interval', type=int, default=None, help="Time between retries (in seconds).")
    parser.add_argument('--court_mode', action='store_true', help="Enable court database mode to save all court entries to SQLite.")
    parser.add_argument('--db_path', default=None, help="Path to SQLite database file (default: .sqlite/court_entries.db).")
    parser.add_argument('--court_codes', help="Comma-separated list of court codes (e.g., 'almd,alsd,akd'). If not provided, uses default list.")
    parser.add_argument('--query_db', action='store_true', help="Query the database and show recent entries.")
    parser.add_argument('--db_stats', action='store_true', help="Show database statistics.")
    parser.add_argument('--query_court', help="Query entries for a specific court code.")
    parser.add_argument('--query_limit', type=int, default=10, help="Limit number of results when querying (default: 10).")
    parser.add_argument('--clean_db', action='store_true', help="Remove entries based on multiple filters: non-complaints, USA v., United States of America v., Commissioner of Social Security, Commissioner of the Social Security Administration, County Prosecutor, and Sheriff's Office cases.")
    parser.add_argument('--migrate_db', action='store_true', help="Migrate existing entries to populate docket_number and case_title fields.")
    parser.add_argument('--remove', action='store_true', help="Remove entries based on field and value criteria. Must be used with --field and --value.")
    parser.add_argument('--field', help="Field name to filter by when using --remove (e.g., 'docket_number', 'court_code', 'case_title').")
    parser.add_argument('--value', help="Field value to match when using --remove.")
    parser.add_argument('--case_title', nargs='?', const=True, help="Remove entries with this case title. Can be used as: --case_title 'CASE NAME' OR --remove --case_title --value 'CASE NAME'.")
    parser.add_argument('--interactive_filter', action='store_true', help="Launch interactive CLI for managing database filtering rules.")
    
    # Blocked phrases arguments
    parser.add_argument('--add-blocked-phrase', action='store_true', help="Add a blocked phrase that removes existing entries and blocks future ones.")
    parser.add_argument('--remove-blocked-phrase', action='store_true', help="Remove a blocked phrase from the database.")
    parser.add_argument('--list-blocked-phrases', action='store_true', help="List all blocked phrases.")
    parser.add_argument('--phrase', help="The phrase to add or remove.")
    parser.add_argument('--phrase-field', default='case_title', help="Field to check for the phrase (default: case_title). Options: title, case_title, docket_number, summary, link_text.")
    parser.add_argument('--no-word-boundary', action='store_true', help="Disable word boundary matching (exact word matching).")
    parser.add_argument('--case-sensitive', action='store_true', help="Enable case-sensitive phrase matching.")

    args = parser.parse_args()
    
    # If no arguments provided, default to court mode
    if len(sys.argv) == 1:
        print("No arguments provided. Starting court monitoring mode...")
        args.court_mode = True

    # Default court codes list
    default_court_codes = [
        "almd", "alsd", "akd", "ared", "arwd",
        "cacd", "cand", "casd", "ctd", "ded", "dcd",
        "flmd", "flsd", "gamd", "gud", "idd", "ilcd",
        "ilnd", "innd", "iand", "iasd", "ksd", "kywd",
        "laed", "lamd", "lawd", "mad", "mied", "miwd",
        "moed", "mowd", "mtd", "ned", "nhd", "njd",
        "nyed", "nynd", "nysd", "nced", "ncmd", "ncwd",
        "nmid", "ohnd", "ohsd", "okwd", "paed", "pawd",
        "prd", "rid", "sdd", "tned", "tnmd", "txed",
        "txsd", "txwd", "utd", "vtd", "vid", "waed",
        "wvnd", "wvsd", "wied", "wiwd", "wyd"
    ]

    # Handle interactive filter CLI
    if args.interactive_filter:
        try:
            from .interactive_filter import DatabaseFilterManager
        except ImportError:
            from interactive_filter import DatabaseFilterManager
        
        try:
            manager = DatabaseFilterManager(args.db_path)
            manager.run()
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        except Exception as e:
            print(f"Error: {e}")
        return

    # Handle database query operations
    if args.query_db or args.db_stats or args.query_court or args.clean_db or args.migrate_db or args.remove or args.case_title or args.add_blocked_phrase or args.remove_blocked_phrase or args.list_blocked_phrases:
        # Use default path if none specified
        db_path = args.db_path
        if db_path is None:
            db_path = os.path.join(".sqlite", "court_entries.db")
        
        # Handle blocked phrases operations (create database if needed)
        if args.add_blocked_phrase or args.remove_blocked_phrase or args.list_blocked_phrases:
            if not os.path.exists(db_path):
                # Initialize database for blocked phrases operations
                print(f"Database not found. Creating database at {db_path}...")
                init_database(db_path)
        elif not os.path.exists(db_path):
            print(f"Error: Database file {db_path} does not exist.")
            print("Run with --court_mode first to create the database.")
            sys.exit(1)
        
        if args.add_blocked_phrase:
            if not args.phrase:
                print("Error: --add-blocked-phrase requires --phrase argument.")
                print("Example: python -m courtrss.rss_feed --add-blocked-phrase --phrase 'Plaintiff(s) v. Defendant(s)'")
                sys.exit(1)
            
            word_boundary = not args.no_word_boundary
            success, deleted_count = add_blocked_phrase(
                db_path, 
                args.phrase, 
                args.phrase_field, 
                word_boundary, 
                args.case_sensitive
            )
            
            if success:
                print(f"✓ Added blocked phrase: '{args.phrase}' for field '{args.phrase_field}'")
                if deleted_count > 0:
                    print(f"✓ Removed {deleted_count} existing entries matching this phrase")
            else:
                print(f"Failed to add blocked phrase")
            return
        
        if args.remove_blocked_phrase:
            if not args.phrase:
                print("Error: --remove-blocked-phrase requires --phrase argument.")
                print("Example: python -m courtrss.rss_feed --remove-blocked-phrase --phrase 'Plaintiff(s) v. Defendant(s)'")
                sys.exit(1)
            
            success = remove_blocked_phrase(db_path, args.phrase, args.phrase_field)
            if success:
                print(f"✓ Removed blocked phrase: '{args.phrase}' for field '{args.phrase_field}'")
            else:
                print(f"Blocked phrase not found: '{args.phrase}' for field '{args.phrase_field}'")
            return
        
        if args.list_blocked_phrases:
            blocked_phrases = get_blocked_phrases(db_path)
            if blocked_phrases:
                print(f"\n=== Blocked Phrases ({len(blocked_phrases)} total) ===")
                for phrase_info in blocked_phrases:
                    word_boundary = "Yes" if phrase_info['word_boundary'] else "No"
                    case_sensitive = "Yes" if phrase_info['case_sensitive'] else "No"
                    print(f"\nPhrase: '{phrase_info['phrase']}'")
                    print(f"  Field: {phrase_info['field']}")
                    print(f"  Word Boundary: {word_boundary}")
                    print(f"  Case Sensitive: {case_sensitive}")
                    print(f"  Added: {phrase_info['created_at']}")
            else:
                print("No blocked phrases found.")
            return
        
        if args.clean_db:
            print("Cleaning database - applying all filters...")
            print("Removing: non-complaints, USA v., United States of America v., Commissioner of Social Security, Commissioner of the Social Security Administration, County Prosecutor, Sheriff's Office cases, placeholder entries")
            deleted_count = clean_non_complaint_entries(db_path)
            print(f"Removed {deleted_count} filtered entries.")
            return
        
        if args.migrate_db:
            print("Migrating existing entries to populate docket_number and case_title fields...")
            migrated_count = migrate_existing_entries(db_path)
            print(f"Migrated {migrated_count} entries.")
            return
        
        if args.remove or args.case_title:
            # Handle different usage patterns for --case_title
            if args.case_title and args.case_title is not True:
                # Pattern: --case_title "CASE NAME"
                if args.field or args.value or args.remove:
                    print("Error: --case_title with value cannot be used with --field, --value, or --remove arguments.")
                    print("Use either: --case_title 'CASE NAME' OR --remove --case_title --value 'CASE NAME'")
                    sys.exit(1)
                field_name = 'case_title'
                field_value = args.case_title
                print(f"Removing entries with case title: '{field_value}'...")
            elif args.case_title is True and args.remove and args.value:
                # Pattern: --remove --case_title --value "CASE NAME"
                if args.field:
                    print("Error: Cannot use both --case_title and --field arguments.")
                    print("Use either: --remove --case_title --value 'CASE NAME' OR --remove --field case_title --value 'CASE NAME'")
                    sys.exit(1)
                field_name = 'case_title'
                field_value = args.value
                print(f"Removing entries with case title: '{field_value}'...")
            elif args.remove:
                # Pattern: --remove --field FIELD --value VALUE
                if not args.field or not args.value:
                    print("Error: --remove requires both --field and --value arguments.")
                    print("Examples:")
                    print("  python -m courtrss.rss_feed --remove --field docket_number --value '1:25-cv-04011'")
                    print("  python -m courtrss.rss_feed --remove --case_title --value 'CASE NAME'")
                    print("  python -m courtrss.rss_feed --case_title 'CASE NAME'")
                    sys.exit(1)
                field_name = args.field
                field_value = args.value
                print(f"Removing entries where {field_name} = '{field_value}'...")
            else:
                print("Error: Invalid argument combination.")
                print("Valid usage patterns:")
                print("  python -m courtrss.rss_feed --case_title 'CASE NAME'")
                print("  python -m courtrss.rss_feed --remove --case_title --value 'CASE NAME'")
                print("  python -m courtrss.rss_feed --remove --field case_title --value 'CASE NAME'")
                sys.exit(1)
            
            deleted_count = remove_entries_by_field(db_path, field_name, field_value)
            print(f"Removed {deleted_count} entries.")
            return
        
        if args.db_stats:
            stats = get_database_stats(db_path)
            if stats:
                print(f"\n=== Database Statistics ===")
                print(f"Total entries: {stats['total_entries']}")
                print(f"Complaint entries: {stats['complaint_entries']}")
                print(f"\nEntries by court:")
                for court_code, count in stats['court_stats'][:10]:  # Show top 10
                    print(f"  {court_code}: {count}")
                if stats['latest_entry']:
                    court, title, created = stats['latest_entry']
                    print(f"\nLatest entry: {court} - {title} ({created})")
            return
        
        if args.query_db or args.query_court:
            entries = query_court_entries(
                db_path, 
                court_code=args.query_court, 
                limit=args.query_limit
            )
            
            if entries:
                print(f"\n=== Recent Court Entries ===")
                for entry in entries:
                    print(f"\nCourt: {entry['court_code']}")
                    if entry.get('docket_number'):
                        print(f"Docket: {entry['docket_number']}")
                    if entry.get('title'):
                        print(f"Title: {entry['title']}")
                    print(f"Link Text: {entry['link_text']}")
                    print(f"Published: {entry['published_date'] or 'N/A'}")
                    print(f"Created: {entry['created_at']}")
                    if entry['link_url']:
                        print(f"Link: {entry['link_url']}")
                    print("-" * 50)
            else:
                print("No entries found.")
            return

    # Handle court database mode
    if args.court_mode:
        if args.court_codes:
            court_codes = [code.strip() for code in args.court_codes.split(',')]
        else:
            court_codes = default_court_codes
        
        # Initialize database
        db_path = init_database(args.db_path)
        
        # Clean existing non-complaint entries
        print("Cleaning existing database entries...")
        deleted_count = clean_non_complaint_entries(db_path)
        if deleted_count > 0:
            print(f"Removed {deleted_count} existing non-complaint entries.")
        
        # Validate arguments for court mode
        interval = args.interval
        retries = args.retries
        retry_interval = args.retry_interval
        
        if interval <= 0:
            print("Error: Interval must be greater than 0.")
            sys.exit(1)
        if retries < 0:
            print("Error: Retries must be 0 or greater.")
            sys.exit(1)
        if not retry_interval:
            retry_interval = interval / max(retries, 1)
        elif retry_interval <= 0:
            print("Error: Retry interval must be greater than 0.")
            sys.exit(1)
        
        print(f"Starting court monitoring for {len(court_codes)} courts...")
        print(f"Database: {db_path}")
        print(f"Interval: {interval} seconds")
        print("Press Ctrl+C to exit.")
        
        monitoring_thread = Thread(target=start_court_monitoring, args=(court_codes, db_path, interval, retries, retry_interval))
        monitoring_thread.daemon = True
        monitoring_thread.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nStopping court monitoring...")
            sys.exit(0)

    if args.config:
        try:
            rss_urls, keywords, notify_methods, interval, retries, retry_interval = parse_yaml_config(args.config)
        except Exception as e:
            logging.error(f"Error loading config file: {e}")
            sys.exit(1)
    elif args.rss_urls and args.keywords:
        rss_urls = [url.strip() for url in args.rss_urls.split(',')]
        keywords = [keyword.strip() for keyword in args.keywords.split(',')]
        notify_methods = [{'type': 'window_notification'}]
        interval = args.interval
        retries = args.retries
        retry_interval = args.retry_interval
    else:
        logging.error("Error: You must provide either --rss_urls and --keywords or --config.")
        sys.exit(1)

    if not rss_urls or len(rss_urls) == 0:
        print("Error: No RSS feed URLs provided.")
        sys.exit(1)
    if not keywords or len(keywords) == 0:
        print("Error: No keywords provided.")
        sys.exit(1)
    if not notify_methods or len(notify_methods) == 0:
        print("Error: No notification methods provided.")
        sys.exit(1)
    if interval <= 0:
        print("Error: Interval must be greater than 0.")
        sys.exit(1)
    if retries < 0:
        print("Error: Retries must be 0 or greater.")
        sys.exit(1)
    if not retry_interval:
        retry_interval = interval / max(retries, 1)
    elif retry_interval <= 0:
        print("Error: Retry interval must be greater than 0.")
        sys.exit(1)

    monitoring_thread = Thread(target=start_monitoring, args=(rss_urls, keywords, interval, notify_methods, retries, retry_interval))
    monitoring_thread.daemon = True
    monitoring_thread.start()

    print("Monitoring RSS feeds... Press Ctrl+C to exit.")
    while True:
        time.sleep(1)

if __name__ == "__main__":
    main()
