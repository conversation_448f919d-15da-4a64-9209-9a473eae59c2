#!/usr/bin/env python3
"""
Interactive CLI for managing court database filtering rules.
Uses rich formatting for a beautiful command-line interface.
"""

import os
import sys
import sqlite3
import logging
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.prompt import Prompt, Confirm
    from rich.text import Text
    from rich.layout import Layout
    from rich.live import Live
    from rich.align import Align
    from rich.columns import Columns
    from rich.rule import Rule
    from rich import box
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Error: rich library not available. Please install with: pip install rich")
    sys.exit(1)

# Import the existing functions from rss_feed
try:
    from .rss_feed import (
        init_database, get_database_stats, query_court_entries,
        clean_non_complaint_entries, remove_entries_by_field
    )
except ImportError:
    # Fallback for direct execution
    from rss_feed import (
        init_database, get_database_stats, query_court_entries,
        clean_non_complaint_entries, remove_entries_by_field
    )

console = Console()

class FilterType(Enum):
    EQUALS = "equals"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"

@dataclass
class FilterRule:
    """Represents a database filtering rule."""
    field: str
    operator: FilterType
    value: str
    description: str = ""
    case_sensitive: bool = False
    active: bool = True

class DatabaseFilterManager:
    """Manages database filtering rules and operations."""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or os.path.join(".sqlite", "court_entries.db")
        self.console = Console()
        
        # Valid database fields
        self.valid_fields = [
            'id', 'court_code', 'docket_number', 'case_title', 'title',
            'summary', 'link_text', 'link_url', 'published_date', 'entry_id',
            'feed_url', 'created_at'
        ]
        
        # Initialize with existing hardcoded rules
        self.rules = self._get_existing_rules()
    
    def _get_existing_rules(self) -> List[FilterRule]:
        """Get the existing hardcoded filtering rules from the codebase."""
        return [
            FilterRule(
                field="link_text",
                operator=FilterType.EQUALS,
                value="NULL",
                description="Remove entries with no link text",
                active=True
            ),
            FilterRule(
                field="link_text",
                operator=FilterType.EQUALS,
                value="Complaint",
                description="Keep only Complaint entries (inverted logic)",
                active=True
            ),
            FilterRule(
                field="link_text",
                operator=FilterType.EQUALS,
                value="Complaint, Amended",
                description="Keep only Amended Complaint entries (inverted logic)",
                active=True
            ),
            FilterRule(
                field="title",
                operator=FilterType.CONTAINS,
                value="USA v.",
                description="Remove criminal cases filed by USA",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="title",
                operator=FilterType.CONTAINS,
                value="United States of America v.",
                description="Remove criminal cases filed by United States",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.STARTS_WITH,
                value="USA v.",
                description="Remove criminal cases (parsed title)",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.STARTS_WITH,
                value="United States of America v.",
                description="Remove criminal cases (parsed title)",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.CONTAINS,
                value="commissioner of social security",
                description="Remove Social Security disability cases",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.CONTAINS,
                value="commissioner of the social security administration",
                description="Remove Social Security Administration cases",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.CONTAINS,
                value="county prosecutor",
                description="Remove County Prosecutor cases",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="case_title",
                operator=FilterType.CONTAINS,
                value="social security administration",
                description="Remove any Social Security Administration cases",
                case_sensitive=False,
                active=True
            ),
            FilterRule(
                field="title",
                operator=FilterType.EQUALS,
                value="1:99-mc-09999 Plaintiff(s) v. Defendant(s)",
                description="Remove placeholder entries",
                active=True
            ),
            FilterRule(
                field="docket_number",
                operator=FilterType.EQUALS,
                value="1:99-mc-09999",
                description="Remove placeholder docket numbers",
                active=True
            ),
            FilterRule(
                field="docket_number",
                operator=FilterType.CONTAINS,
                value="-mj-",
                description="Remove magistrate judge cases",
                active=True
            ),
        ]
    
    def show_header(self):
        """Display the application header."""
        header = Panel.fit(
            "[bold blue]Court Database Filter Manager[/bold blue]\n"
            "[dim]Interactive CLI for managing database filtering rules[/dim]",
            border_style="blue"
        )
        self.console.print(header)
        self.console.print()
    
    def show_database_info(self):
        """Display current database information."""
        if not os.path.exists(self.db_path):
            self.console.print(f"[red]Database not found: {self.db_path}[/red]")
            return
        
        stats = get_database_stats(self.db_path)
        if stats:
            info_table = Table(title="Database Information", box=box.ROUNDED)
            info_table.add_column("Metric", style="cyan")
            info_table.add_column("Value", style="green")
            
            info_table.add_row("Database Path", self.db_path)
            info_table.add_row("Total Entries", str(stats['total_entries']))
            info_table.add_row("Complaint Entries", str(stats['complaint_entries']))
            
            if stats['latest_entry']:
                court, title, created = stats['latest_entry']
                info_table.add_row("Latest Entry", f"{court} - {title[:50]}...")
                info_table.add_row("Latest Date", created)
            
            self.console.print(info_table)
            self.console.print()
    
    def show_rules_table(self):
        """Display current filtering rules in a table."""
        table = Table(title="Current Filtering Rules", box=box.ROUNDED)
        table.add_column("#", style="dim", width=3)
        table.add_column("Field", style="cyan")
        table.add_column("Operator", style="yellow")
        table.add_column("Value", style="green")
        table.add_column("Description", style="white")
        table.add_column("Case Sensitive", style="magenta", width=12)
        table.add_column("Active", style="red", width=6)
        
        for i, rule in enumerate(self.rules, 1):
            status = "✓" if rule.active else "✗"
            case_sensitive = "Yes" if rule.case_sensitive else "No"
            
            # Truncate long values
            value_display = rule.value[:30] + "..." if len(rule.value) > 30 else rule.value
            desc_display = rule.description[:40] + "..." if len(rule.description) > 40 else rule.description
            
            table.add_row(
                str(i),
                rule.field,
                rule.operator.value,
                value_display,
                desc_display,
                case_sensitive,
                status
            )
        
        self.console.print(table)
        self.console.print()
    
    def add_rule_interactive(self):
        """Interactive rule addition."""
        self.console.print(Panel("[bold green]Add New Filtering Rule[/bold green]", border_style="green"))
        
        # Field selection
        self.console.print("\n[cyan]Available fields:[/cyan]")
        for i, field in enumerate(self.valid_fields, 1):
            self.console.print(f"  {i:2d}. {field}")
        
        while True:
            field_choice = Prompt.ask(
                "\nSelect field by number or enter field name",
                choices=[str(i) for i in range(1, len(self.valid_fields) + 1)] + self.valid_fields
            )
            
            if field_choice.isdigit():
                field = self.valid_fields[int(field_choice) - 1]
            else:
                field = field_choice
            
            if field in self.valid_fields:
                break
            else:
                self.console.print(f"[red]Invalid field: {field}[/red]")
        
        # Operator selection
        self.console.print(f"\n[cyan]Selected field:[/cyan] {field}")
        self.console.print("\n[cyan]Available operators:[/cyan]")
        operators = list(FilterType)
        for i, op in enumerate(operators, 1):
            self.console.print(f"  {i}. {op.value}")
        
        while True:
            op_choice = Prompt.ask(
                "\nSelect operator by number",
                choices=[str(i) for i in range(1, len(operators) + 1)]
            )
            operator = operators[int(op_choice) - 1]
            break
        
        # Value input
        self.console.print(f"\n[cyan]Selected operator:[/cyan] {operator.value}")
        value = Prompt.ask("\nEnter the value to filter by")
        
        # Case sensitivity (only for string operations)
        case_sensitive = False
        if operator in [FilterType.CONTAINS, FilterType.STARTS_WITH, FilterType.ENDS_WITH]:
            case_sensitive = Confirm.ask("Case sensitive matching?", default=False)
        
        # Description
        description = Prompt.ask("Enter a description for this rule (optional)", default="")
        
        # Create and add the rule
        new_rule = FilterRule(
            field=field,
            operator=operator,
            value=value,
            description=description,
            case_sensitive=case_sensitive,
            active=True
        )
        
        self.rules.append(new_rule)
        
        self.console.print(f"\n[green]✓ Rule added successfully![/green]")
        self.console.print(f"Field: {field}")
        self.console.print(f"Operator: {operator.value}")
        self.console.print(f"Value: {value}")
        self.console.print(f"Case Sensitive: {case_sensitive}")
        self.console.print(f"Description: {description}")
    
    def toggle_rule(self):
        """Toggle a rule's active status."""
        if not self.rules:
            self.console.print("[red]No rules available to toggle.[/red]")
            return
        
        self.show_rules_table()
        
        while True:
            try:
                rule_num = int(Prompt.ask(f"Enter rule number to toggle (1-{len(self.rules)})"))
                if 1 <= rule_num <= len(self.rules):
                    rule = self.rules[rule_num - 1]
                    rule.active = not rule.active
                    status = "activated" if rule.active else "deactivated"
                    self.console.print(f"[green]✓ Rule {rule_num} {status}[/green]")
                    break
                else:
                    self.console.print(f"[red]Invalid rule number. Please enter 1-{len(self.rules)}[/red]")
            except ValueError:
                self.console.print("[red]Please enter a valid number.[/red]")
    
    def delete_rule(self):
        """Delete a rule."""
        if not self.rules:
            self.console.print("[red]No rules available to delete.[/red]")
            return
        
        self.show_rules_table()
        
        while True:
            try:
                rule_num = int(Prompt.ask(f"Enter rule number to delete (1-{len(self.rules)})"))
                if 1 <= rule_num <= len(self.rules):
                    rule = self.rules[rule_num - 1]
                    
                    # Show rule details
                    self.console.print(f"\n[yellow]Rule to delete:[/yellow]")
                    self.console.print(f"Field: {rule.field}")
                    self.console.print(f"Operator: {rule.operator.value}")
                    self.console.print(f"Value: {rule.value}")
                    self.console.print(f"Description: {rule.description}")
                    
                    if Confirm.ask("\nAre you sure you want to delete this rule?"):
                        del self.rules[rule_num - 1]
                        self.console.print(f"[green]✓ Rule {rule_num} deleted[/green]")
                    else:
                        self.console.print("[yellow]Deletion cancelled[/yellow]")
                    break
                else:
                    self.console.print(f"[red]Invalid rule number. Please enter 1-{len(self.rules)}[/red]")
            except ValueError:
                self.console.print("[red]Please enter a valid number.[/red]")
    
    def preview_filter_impact(self):
        """Preview how many entries would be affected by current rules."""
        if not os.path.exists(self.db_path):
            self.console.print(f"[red]Database not found: {self.db_path}[/red]")
            return
        
        self.console.print(Panel("[bold yellow]Filter Impact Preview[/bold yellow]", border_style="yellow"))
        
        # Get current stats
        stats = get_database_stats(self.db_path)
        if not stats:
            self.console.print("[red]Could not get database statistics[/red]")
            return
        
        total_entries = stats['total_entries']
        
        # Count entries that would be affected by each active rule
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        impact_table = Table(title="Rule Impact Analysis", box=box.ROUNDED)
        impact_table.add_column("Rule", style="cyan")
        impact_table.add_column("Field", style="yellow")
        impact_table.add_column("Operator", style="green")
        impact_table.add_column("Value", style="white")
        impact_table.add_column("Matches", style="red")
        impact_table.add_column("% of Total", style="magenta")
        
        total_affected = 0
        
        for i, rule in enumerate(self.rules, 1):
            if not rule.active:
                continue
            
            try:
                # Build query based on operator
                if rule.operator == FilterType.EQUALS:
                    query = f"SELECT COUNT(*) FROM court_entries WHERE {rule.field} = ?"
                    params = (rule.value,)
                elif rule.operator == FilterType.CONTAINS:
                    if rule.case_sensitive:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE {rule.field} LIKE ?"
                        params = (f"%{rule.value}%",)
                    else:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE LOWER({rule.field}) LIKE LOWER(?)"
                        params = (f"%{rule.value}%",)
                elif rule.operator == FilterType.STARTS_WITH:
                    if rule.case_sensitive:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE {rule.field} LIKE ?"
                        params = (f"{rule.value}%",)
                    else:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE LOWER({rule.field}) LIKE LOWER(?)"
                        params = (f"{rule.value}%",)
                elif rule.operator == FilterType.ENDS_WITH:
                    if rule.case_sensitive:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE {rule.field} LIKE ?"
                        params = (f"%{rule.value}",)
                    else:
                        query = f"SELECT COUNT(*) FROM court_entries WHERE LOWER({rule.field}) LIKE LOWER(?)"
                        params = (f"%{rule.value}",)
                
                cursor.execute(query, params)
                matches = cursor.fetchone()[0]
                percentage = (matches / total_entries * 100) if total_entries > 0 else 0
                
                # Truncate long values for display
                value_display = rule.value[:20] + "..." if len(rule.value) > 20 else rule.value
                
                impact_table.add_row(
                    str(i),
                    rule.field,
                    rule.operator.value,
                    value_display,
                    str(matches),
                    f"{percentage:.1f}%"
                )
                
                total_affected += matches
                
            except sqlite3.Error as e:
                impact_table.add_row(
                    str(i),
                    rule.field,
                    rule.operator.value,
                    rule.value[:20],
                    "ERROR",
                    "N/A"
                )
        
        conn.close()
        
        self.console.print(impact_table)
        
        # Summary
        summary_table = Table(box=box.ROUNDED)
        summary_table.add_column("Summary", style="bold cyan")
        summary_table.add_column("Count", style="bold white")
        
        summary_table.add_row("Total Entries", str(total_entries))
        summary_table.add_row("Entries Matching Rules", str(min(total_affected, total_entries)))
        summary_table.add_row("Estimated Remaining", str(max(0, total_entries - total_affected)))
        
        self.console.print()
        self.console.print(summary_table)
    
    def apply_filters(self):
        """Apply the current filtering rules to the database."""
        if not os.path.exists(self.db_path):
            self.console.print(f"[red]Database not found: {self.db_path}[/red]")
            return
        
        active_rules = [rule for rule in self.rules if rule.active]
        if not active_rules:
            self.console.print("[yellow]No active rules to apply.[/yellow]")
            return
        
        self.console.print(Panel("[bold red]Apply Filters to Database[/bold red]", border_style="red"))
        self.console.print(f"[yellow]This will apply {len(active_rules)} active filtering rules to the database.[/yellow]")
        self.console.print("[yellow]This action cannot be undone![/yellow]")
        
        if not Confirm.ask("\nAre you sure you want to proceed?"):
            self.console.print("[yellow]Operation cancelled[/yellow]")
            return
        
        # Apply custom rules
        total_deleted = self._apply_custom_rules(active_rules)
        
        self.console.print(f"[green]✓ Filters applied successfully![/green]")
        self.console.print(f"[green]Removed {total_deleted} entries from database[/green]")
    
    def _apply_custom_rules(self, rules: List[FilterRule]) -> int:
        """Apply custom filtering rules to the database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Count entries before cleanup
        cursor.execute("SELECT COUNT(*) FROM court_entries")
        total_before = cursor.fetchone()[0]
        
        total_deleted = 0
        
        with self.console.status("[bold green]Applying filters...") as status:
            for i, rule in enumerate(rules, 1):
                status.update(f"[bold green]Applying rule {i}/{len(rules)}: {rule.field} {rule.operator.value} {rule.value[:20]}...")
                
                try:
                    # Build the WHERE clause based on the rule
                    where_clause, params = self._build_where_clause(rule)
                    
                    # Special handling for link_text rules (inverted logic for keeping complaints)
                    if rule.field == "link_text" and rule.value in ["Complaint", "Complaint, Amended"]:
                        # This is a "keep" rule, so we invert the logic
                        if rule.operator == FilterType.EQUALS:
                            where_clause = f"link_text IS NULL OR link_text NOT IN ('Complaint', 'Complaint, Amended')"
                            params = ()
                    
                    if where_clause:
                        delete_query = f"DELETE FROM court_entries WHERE {where_clause}"
                        cursor.execute(delete_query, params)
                        deleted_count = cursor.rowcount
                        total_deleted += deleted_count
                        
                        self.console.print(f"  [dim]Rule {i}: Removed {deleted_count} entries[/dim]")
                
                except sqlite3.Error as e:
                    self.console.print(f"  [red]Error applying rule {i}: {e}[/red]")
                    continue
        
        conn.commit()
        conn.close()
        
        return total_deleted
    
    def _build_where_clause(self, rule: FilterRule) -> tuple:
        """Build SQL WHERE clause and parameters for a rule."""
        field = rule.field
        value = rule.value
        
        if rule.operator == FilterType.EQUALS:
            if value == "NULL":
                return f"{field} IS NULL", ()
            else:
                return f"{field} = ?", (value,)
        
        elif rule.operator == FilterType.CONTAINS:
            if rule.case_sensitive:
                return f"{field} LIKE ?", (f"%{value}%",)
            else:
                return f"LOWER({field}) LIKE LOWER(?)", (f"%{value}%",)
        
        elif rule.operator == FilterType.STARTS_WITH:
            if rule.case_sensitive:
                return f"{field} LIKE ?", (f"{value}%",)
            else:
                return f"LOWER({field}) LIKE LOWER(?)", (f"{value}%",)
        
        elif rule.operator == FilterType.ENDS_WITH:
            if rule.case_sensitive:
                return f"{field} LIKE ?", (f"%{value}",)
            else:
                return f"LOWER({field}) LIKE LOWER(?)", (f"%{value}",)
        
        return "", ()
    
    def save_rules_to_file(self):
        """Save current rules to a JSON file."""
        if not self.rules:
            self.console.print("[yellow]No rules to save.[/yellow]")
            return
        
        filename = Prompt.ask("Enter filename to save rules", default="filter_rules.json")
        
        try:
            # Convert rules to serializable format
            rules_data = []
            for rule in self.rules:
                rule_dict = asdict(rule)
                rule_dict['operator'] = rule.operator.value  # Convert enum to string
                rules_data.append(rule_dict)
            
            with open(filename, 'w') as f:
                json.dump(rules_data, f, indent=2)
            
            self.console.print(f"[green]✓ Rules saved to {filename}[/green]")
        
        except Exception as e:
            self.console.print(f"[red]Error saving rules: {e}[/red]")
    
    def load_rules_from_file(self):
        """Load rules from a JSON file."""
        filename = Prompt.ask("Enter filename to load rules from", default="filter_rules.json")
        
        if not os.path.exists(filename):
            self.console.print(f"[red]File not found: {filename}[/red]")
            return
        
        try:
            with open(filename, 'r') as f:
                rules_data = json.load(f)
            
            # Convert back to FilterRule objects
            loaded_rules = []
            for rule_dict in rules_data:
                rule_dict['operator'] = FilterType(rule_dict['operator'])  # Convert string back to enum
                loaded_rules.append(FilterRule(**rule_dict))
            
            # Ask if user wants to replace or append
            if self.rules:
                replace = Confirm.ask(f"Replace current {len(self.rules)} rules with {len(loaded_rules)} loaded rules?")
                if replace:
                    self.rules = loaded_rules
                else:
                    self.rules.extend(loaded_rules)
            else:
                self.rules = loaded_rules
            
            self.console.print(f"[green]✓ Loaded {len(loaded_rules)} rules from {filename}[/green]")
        
        except Exception as e:
            self.console.print(f"[red]Error loading rules: {e}[/red]")
    
    def show_menu(self):
        """Display the main menu."""
        menu_text = """
[bold cyan]Main Menu[/bold cyan]

[green]1.[/green] View Current Rules
[green]2.[/green] Add New Rule
[green]3.[/green] Toggle Rule Active/Inactive
[green]4.[/green] Delete Rule
[green]5.[/green] Preview Filter Impact
[green]6.[/green] Apply Filters to Database
[green]7.[/green] Save Rules to File
[green]8.[/green] Load Rules from File
[green]9.[/green] Show Database Info
[green]10.[/green] Exit

"""
        self.console.print(Panel(menu_text, border_style="cyan"))
    
    def run(self):
        """Run the interactive CLI."""
        self.show_header()
        
        while True:
            self.show_menu()
            
            choice = Prompt.ask(
                "Select an option",
                choices=["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
                default="1"
            )
            
            self.console.clear()
            self.show_header()
            
            if choice == "1":
                self.show_rules_table()
            elif choice == "2":
                self.add_rule_interactive()
            elif choice == "3":
                self.toggle_rule()
            elif choice == "4":
                self.delete_rule()
            elif choice == "5":
                self.preview_filter_impact()
            elif choice == "6":
                self.apply_filters()
            elif choice == "7":
                self.save_rules_to_file()
            elif choice == "8":
                self.load_rules_from_file()
            elif choice == "9":
                self.show_database_info()
            elif choice == "10":
                self.console.print("[green]Goodbye![/green]")
                break
            
            if choice != "10":
                self.console.print("\n" + "─" * 50)
                Prompt.ask("Press Enter to continue", default="")
                self.console.clear()

def main():
    """Main entry point for the interactive filter CLI."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Interactive Court Database Filter Manager")
    parser.add_argument('--db_path', help="Path to SQLite database file")
    
    args = parser.parse_args()
    
    if not RICH_AVAILABLE:
        print("Error: This tool requires the 'rich' library. Install with: pip install rich")
        sys.exit(1)
    
    try:
        manager = DatabaseFilterManager(args.db_path)
        manager.run()
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()