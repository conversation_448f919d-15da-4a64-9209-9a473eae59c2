# Court Database Mode

The CourtRSS package has been updated to support saving all court information into a local SQLite database. This mode monitors RSS feeds from federal district courts and stores all entries for later analysis.

## New Features

### Court Database Mode
- **Automatic court monitoring**: Monitors RSS feeds from all federal district courts
- **SQLite storage**: Saves all court entries to a local database
- **Duplicate prevention**: Avoids saving duplicate entries
- **Database queries**: Query and analyze stored court data

### Supported Courts
The system supports 69 federal district courts:
```
almd, alsd, akd, ared, arwd, cacd, cand, casd, ctd, ded, dcd,
flmd, flsd, gamd, gud, idd, ilcd, ilnd, innd, iand, iasd, ksd, 
kywd, laed, lamd, lawd, mad, mied, miwd, moed, mowd, mtd, ned, 
nhd, njd, nyed, nynd, nysd, nced, ncmd, ncwd, nmid, ohnd, ohsd, 
okwd, paed, pawd, prd, rid, sdd, tned, tnmd, txed, txsd, txwd, 
utd, vtd, vid, waed, wvnd, wvsd, wied, wiwd, wyd
```

## Usage

### Start Court Monitoring
Monitor all courts and save to database:
```bash
courtrss --court_mode
```

Monitor specific courts:
```bash
courtrss --court_mode --court_codes "almd,alsd,cacd"
```

Custom database path and interval:
```bash
courtrss --court_mode --db_path ".sqlite/my_court_data.db" --interval 120
```

### Query Database
Show database statistics:
```bash
courtrss --db_stats
```

Show recent entries:
```bash
courtrss --query_db --query_limit 20
```

Query specific court:
```bash
courtrss --query_court almd --query_limit 10
```

## Database Schema

The SQLite database contains a `court_entries` table with the following columns:

- `id`: Primary key (auto-increment)
- `court_code`: Court identifier (e.g., 'almd', 'cacd')
- `title`: Entry title
- `summary`: Entry summary/description
- `link_text`: Extracted link text
- `link_url`: Extracted URL
- `published_date`: Publication date from RSS feed
- `entry_id`: Unique entry identifier from RSS
- `feed_url`: Source RSS feed URL
- `created_at`: Timestamp when entry was saved

## Command Line Options

### Court Mode Options
- `--court_mode`: Enable court database mode
- `--court_codes`: Comma-separated list of court codes
- `--db_path`: Path to SQLite database file (default: .sqlite/court_entries.db)
- `--interval`: Check interval in seconds (default: 60)
- `--retries`: Number of retries for failed feeds (default: 3)
- `--retry_interval`: Time between retries in seconds

### Query Options
- `--query_db`: Query and display recent entries
- `--db_stats`: Show database statistics
- `--query_court`: Query entries for specific court
- `--query_limit`: Limit number of results (default: 10)

## Example Usage

```python
# Start monitoring all courts
courtrss --court_mode

# Monitor specific courts with custom settings
courtrss --court_mode --court_codes "almd,cacd,nysd" --interval 300 --db_path ".sqlite/courts.db"

# Check what's been collected
courtrss --db_stats --db_path ".sqlite/courts.db"

# View recent entries from Alabama Middle District
courtrss --query_court almd --query_limit 5 --db_path ".sqlite/courts.db"
```

## Database Analysis

You can also access the SQLite database directly for custom analysis:

```python
import sqlite3

conn = sqlite3.connect('.sqlite/court_entries.db')
cursor = conn.cursor()

# Find entries with specific keywords
cursor.execute("""
    SELECT court_code, title, published_date 
    FROM court_entries 
    WHERE title LIKE '%patent%' OR summary LIKE '%patent%'
    ORDER BY created_at DESC
""")

results = cursor.fetchall()
for court, title, date in results:
    print(f"{court}: {title} ({date})")

conn.close()
```

## Integration with Existing Features

The court database mode works alongside the existing keyword monitoring features. You can:

1. Run court database mode to collect all data
2. Use the original keyword monitoring for real-time alerts
3. Query the database for historical analysis

Both modes can run simultaneously with different configurations.