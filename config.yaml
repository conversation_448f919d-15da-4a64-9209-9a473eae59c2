# CourtRSS Configuration File
# This file documents the filtering rules and settings for the CourtRSS tool

# Database settings
database:
  path: ".sqlite/court_entries.db"
  
# Monitoring settings
monitoring:
  interval: 180  # Check interval in seconds
  retries: 3    # Number of retries for failed feeds
  retry_interval: 60  # Time between retries in seconds

# Court codes to monitor (all 62 federal district courts)
court_codes:
  - almd  # Middle District of Alabama
  - alsd  # Southern District of Alabama
  - akd   # District of Alaska
  - ared  # Eastern District of Arkansas
  - arwd  # Western District of Arkansas
  - cacd  # Central District of California
  - cand  # Northern District of California
  - casd  # Southern District of California
  - ctd   # District of Connecticut
  - ded   # District of Delaware
  - dcd   # District of Columbia
  - flmd  # Middle District of Florida
  - flsd  # Southern District of Florida
  - gamd  # Middle District of Georgia
  - gud   # District of Guam
  - idd   # District of Idaho
  - ilcd  # Central District of Illinois
  - ilnd  # Northern District of Illinois
  - innd  # Northern District of Indiana
  - iand  # Northern District of Iowa
  - iasd  # Southern District of Iowa
  - ksd   # District of Kansas
  - kywd  # Western District of Kentucky
  - laed  # Eastern District of Louisiana
  - lamd  # Middle District of Louisiana
  - lawd  # Western District of Louisiana
  - mad   # District of Massachusetts
  - mied  # Eastern District of Michigan
  - miwd  # Western District of Michigan
  - moed  # Eastern District of Missouri
  - mowd  # Western District of Missouri
  - mtd   # District of Montana
  - ned   # District of Nebraska
  - nhd   # District of New Hampshire
  - njd   # District of New Jersey
  - nyed  # Eastern District of New York
  - nynd  # Northern District of New York
  - nysd  # Southern District of New York
  - nced  # Eastern District of North Carolina
  - ncmd  # Middle District of North Carolina
  - ncwd  # Western District of North Carolina
  - nmid  # District of New Mexico
  - ohnd  # Northern District of Ohio
  - ohsd  # Southern District of Ohio
  - okwd  # Western District of Oklahoma
  - paed  # Eastern District of Pennsylvania
  - pawd  # Western District of Pennsylvania
  - prd   # District of Puerto Rico
  - rid   # District of Rhode Island
  - sdd   # District of South Dakota
  - tned  # Eastern District of Tennessee
  - tnmd  # Middle District of Tennessee
  - txed  # Eastern District of Texas
  - txsd  # Southern District of Texas
  - txwd  # Western District of Texas
  - utd   # District of Utah
  - vtd   # District of Vermont
  - vid   # District of Virgin Islands
  - waed  # Eastern District of Washington
  - wvnd  # Northern District of West Virginia
  - wvsd  # Southern District of West Virginia
  - wied  # Eastern District of Wisconsin
  - wiwd  # Western District of Wisconsin
  - wyd   # District of Wyoming

# Filtering rules
filters:
  # Document types to include (exact match)
  included_document_types:
    - "Complaint"
    - "Complaint, Amended"
  
  # Case title exclusions
  excluded_case_patterns:
    # Criminal cases
    - pattern: "USA v."
      type: "starts_with"
      description: "Criminal cases filed by USA"
    
    - pattern: "United States of America v."
      type: "starts_with"
      description: "Criminal cases filed by United States of America"
    
    # Social Security cases
    - pattern: "commissioner of social security"
      type: "contains"
      case_sensitive: false
      description: "Social Security disability cases"
    
    - pattern: "commissioner of the social security administration"
      type: "contains"
      case_sensitive: false
      description: "Social Security Administration cases"

    - pattern: "Commissioner  of Social Security"
      type: "contains"
      case_sensitive: false
      description: "Social Security Administration cases"


    # County Prosecutor cases
    - pattern: "county prosecutor"
      type: "contains"
      case_sensitive: false
      description: "Cases involving County Prosecutor"
    
    # Sheriff's Office cases
    - pattern: ["sheriff", "office"]
      type: "contains_all"
      case_sensitive: false
      description: "Cases involving Sheriff's Office"

# Database maintenance commands
maintenance:
  clean_command: "python -m courtrss.rss_feed --clean_db"
  migrate_command: "python -m courtrss.rss_feed --migrate_db"
  
# Query commands
queries:
  stats: "python -m courtrss.rss_feed --db_stats"
  recent_entries: "python -m courtrss.rss_feed --query_db"
  by_court: "python -m courtrss.rss_feed --query_court {court_code}"
  with_limit: "python -m courtrss.rss_feed --query_db --query_limit {limit}"

# Monitoring commands
monitoring_commands:
  all_courts: "python -m courtrss.rss_feed --court_mode"
  specific_courts: "python -m courtrss.rss_feed --court_mode --court_codes '{court_codes}'"
  custom_interval: "python -m courtrss.rss_feed --court_mode --interval {seconds}"