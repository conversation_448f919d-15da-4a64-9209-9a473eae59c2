# CourtRSS - Federal Court RSS Feed Monitor 📡

A Python tool for monitoring US Federal Court RSS feeds and tracking new case filings. This tool can monitor court feeds for specific keywords or save all complaint filings to a SQLite database.

## Features 🚀

- **Monitor 62 US Federal District Court RSS feeds**
- **Database Mode**: Save all complaint filings to SQLite database
- **Smart Filtering**: 
  - Only saves Complaints and Amended Complaints
  - Excludes criminal cases (USA v. cases)
- **Keyword Monitoring**: Search for specific terms in court filings
- **Multiple Notification Methods**:
  - Full-screen green window alerts
  - Discord webhook notifications
- **Robust Error Handling**: Retry logic for failed feeds
- **Query Tools**: Search and analyze collected data

## Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/CourtRSS.git
cd CourtRSS

# Install the package
pip install -e .
```

## Quick Start 🎯

### Database Mode - Monitor All Courts

```bash
# Start monitoring all 62 federal courts
python -m courtrss.rss_feed --court_mode

# Monitor specific courts only
python -m courtrss.rss_feed --court_mode --court_codes "cacd,nysd,txnd"

# Adjust check interval (default is 60 seconds)
python -m courtrss.rss_feed --court_mode --interval 300
```

### Query the Database

```bash
# Show database statistics
python -m courtrss.rss_feed --db_stats

# Query recent entries (default: 10)
python -m courtrss.rss_feed --query_db

# Query more entries
python -m courtrss.rss_feed --query_db --query_limit 50

# Query entries from a specific court
python -m courtrss.rss_feed --query_court cacd --query_limit 20
```

### Database Maintenance

```bash
# Clean database (remove non-complaint entries, USA v. cases, and Social Security cases)
python -m courtrss.rss_feed --clean_db

# Migrate existing entries (populate docket_number and case_title fields)
python -m courtrss.rss_feed --migrate_db
```

## Keyword Monitoring Mode

### Using CLI Arguments

```bash
# Monitor specific feeds for keywords
python -m courtrss.rss_feed \
    --rss_urls "https://ecf.cacd.uscourts.gov/cgi-bin/rss_outside.pl" \
    --keywords "patent,trademark,copyright" \
    --interval 60
```

### Using Configuration File

Create a YAML configuration file:

```yaml
# config.yaml
rss_urls:
  - https://ecf.cacd.uscourts.gov/cgi-bin/rss_outside.pl
  - https://ecf.nysd.uscourts.gov/cgi-bin/rss_outside.pl

keywords:
  - patent
  - copyright
  - trademark

notifications:
  - type: window_notification
  - type: discord_webhook
    webhook_url: https://discord.com/api/webhooks/YOUR_WEBHOOK_URL

interval: 300  # Check every 5 minutes
retries: 3
retry_interval: 60
```

Then run:

```bash
python -m courtrss.rss_feed --config config.yaml
```

## Supported Courts 🏛️

The tool monitors the following 62 federal district courts:

<details>
<summary>Click to see full list of courts</summary>

| Code | Court Name |
|------|------------|
| almd | Middle District of Alabama |
| alsd | Southern District of Alabama |
| akd  | District of Alaska |
| ared | Eastern District of Arkansas |
| arwd | Western District of Arkansas |
| cacd | Central District of California |
| cand | Northern District of California |
| casd | Southern District of California |
| ctd  | District of Connecticut |
| ded  | District of Delaware |
| dcd  | District of Columbia |
| flmd | Middle District of Florida |
| flsd | Southern District of Florida |
| gamd | Middle District of Georgia |
| gud  | District of Guam |
| idd  | District of Idaho |
| ilcd | Central District of Illinois |
| ilnd | Northern District of Illinois |
| innd | Northern District of Indiana |
| iand | Northern District of Iowa |
| iasd | Southern District of Iowa |
| ksd  | District of Kansas |
| kywd | Western District of Kentucky |
| laed | Eastern District of Louisiana |
| lamd | Middle District of Louisiana |
| lawd | Western District of Louisiana |
| mad  | District of Massachusetts |
| mied | Eastern District of Michigan |
| miwd | Western District of Michigan |
| moed | Eastern District of Missouri |
| mowd | Western District of Missouri |
| mtd  | District of Montana |
| ned  | District of Nebraska |
| nhd  | District of New Hampshire |
| njd  | District of New Jersey |
| nyed | Eastern District of New York |
| nynd | Northern District of New York |
| nysd | Southern District of New York |
| nced | Eastern District of North Carolina |
| ncmd | Middle District of North Carolina |
| ncwd | Western District of North Carolina |
| nmid | District of New Mexico |
| ohnd | Northern District of Ohio |
| ohsd | Southern District of Ohio |
| okwd | Western District of Oklahoma |
| paed | Eastern District of Pennsylvania |
| pawd | Western District of Pennsylvania |
| prd  | District of Puerto Rico |
| rid  | District of Rhode Island |
| sdd  | District of South Dakota |
| tned | Eastern District of Tennessee |
| tnmd | Middle District of Tennessee |
| txed | Eastern District of Texas |
| txsd | Southern District of Texas |
| txwd | Western District of Texas |
| utd  | District of Utah |
| vtd  | District of Vermont |
| vid  | District of Virgin Islands |
| waed | Eastern District of Washington |
| wvnd | Northern District of West Virginia |
| wvsd | Southern District of West Virginia |
| wied | Eastern District of Wisconsin |
| wiwd | Western District of Wisconsin |
| wyd  | District of Wyoming |

</details>

## Database Schema 📊

The SQLite database stores entries in the `court_entries` table:

| Field | Description |
|-------|-------------|
| `id` | Primary key |
| `court_code` | Court identifier (e.g., "cacd") |
| `docket_number` | Parsed docket number (e.g., "2:24-cv-12345") |
| `case_title` | Parsed case title (e.g., "Smith v. Jones") |
| `title` | Full entry title from RSS feed |
| `summary` | Entry summary containing document links |
| `link_text` | Document type ("Complaint" or "Complaint, Amended") |
| `link_url` | URL to the document |
| `published_date` | Publication date from RSS feed |
| `entry_id` | Unique entry ID from RSS feed |
| `feed_url` | Source RSS feed URL |
| `created_at` | Timestamp when entry was saved |

## Filtering Rules 🔍

The tool applies the following filters in database mode:

1. **Document Type**: Only saves entries where the link text is exactly:
   - "Complaint"
   - "Complaint, Amended"

2. **Case Type Exclusions**:
   - Criminal cases containing "USA v." or "United States of America v."
   - Magistrate judge cases (docket numbers containing "-mj-")
   - Social Security cases containing "Commissioner of Social Security" (case insensitive)
   - Sheriff's Office cases (containing both "sheriff" and "office", case insensitive)
   - Placeholder entries ("1:99-mc-09999 Plaintiff(s) v. Defendant(s)")
   - Cases with malformed titles (starting with "-" followed by digits)

3. **Docket Number Rules**:
   - Extracts only the 13-character docket format: N:YY-XX-NNNNN
   - Removes any suffixes (e.g., -1, -JPC, -MLK-DAS)
   - Ensures docket numbers are exactly 13 characters

## Command Line Options 🛠️

| Option | Description | Default |
|--------|-------------|---------|
| `--court_mode` | Enable court database mode | False |
| `--court_codes` | Comma-separated list of court codes | All 62 courts |
| `--interval` | Check interval in seconds | 60 |
| `--retries` | Number of retries for failed feeds | 3 |
| `--retry_interval` | Time between retries in seconds | interval/retries |
| `--db_path` | Path to SQLite database | .sqlite/court_entries.db |
| `--query_db` | Query and display recent entries | - |
| `--query_court` | Query entries for specific court | - |
| `--query_limit` | Number of entries to display | 10 |
| `--db_stats` | Show database statistics | - |
| `--clean_db` | Remove filtered entries (non-complaints, USA v., Social Security) | - |
| `--migrate_db` | Migrate existing entries | - |
| `--rss_urls` | Comma-separated RSS URLs (keyword mode) | - |
| `--keywords` | Comma-separated keywords (keyword mode) | - |
| `--config` | Path to YAML configuration file | - |

## Example Usage 💡

### Monitor Patent Cases in California

```bash
# Monitor California courts for patent-related complaints
python -m courtrss.rss_feed --court_mode --court_codes "cacd,cand,casd"
```

### Export Recent Filings

```bash
# Get the last 100 entries as a starting point for analysis
python -m courtrss.rss_feed --query_db --query_limit 100 > recent_filings.txt
```

### Run Example Script

```bash
# Run the included example script
python example_court_monitoring.py
```

## Requirements 📋

- Python 3.6+
- feedparser
- requests
- pyyaml
- sqlite3 (included with Python)
- tkinter (optional, for window notifications)

## Notes 📝

- The tool creates a `.sqlite` directory to store the database
- Each court's RSS feed is checked sequentially to avoid overwhelming servers
- Failed feeds are retried with exponential backoff
- The database uses UNIQUE constraints to prevent duplicate entries
- Database is automatically cleaned on startup to ensure data quality

## Why CourtRSS? 🤷

- **Comprehensive Coverage**: Monitor all federal district courts from one tool
- **Smart Filtering**: Focus on civil complaints, not criminal cases
- **Reliable**: Built-in retry mechanism ensures you don't miss filings
- **Flexible**: Use for real-time alerts or data collection and analysis
- **Open Source**: Customize to your specific needs

## License

MIT License

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.