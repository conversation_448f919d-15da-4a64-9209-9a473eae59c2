#!/usr/bin/env python3
"""
Demo script for the Interactive Court Database Filter Manager.
This script shows how to launch the interactive CLI.
"""

import subprocess
import sys
import os

def main():
    print("=== Court Database Interactive Filter Demo ===")
    print()
    
    # Check if database exists
    db_path = os.path.join(".sqlite", "court_entries.db")
    if os.path.exists(db_path):
        print(f"✓ Database found: {db_path}")
    else:
        print(f"⚠ Database not found: {db_path}")
        print("You can still explore the interface, but some features will be limited.")
    
    print()
    print("Launching Interactive Filter Manager...")
    print("This will open a rich CLI interface for managing database filters.")
    print()
    
    # Launch the interactive filter
    try:
        subprocess.run([
            sys.executable, "-m", "courtrss.rss_feed", "--interactive_filter"
        ])
    except KeyboardInterrupt:
        print("\nDemo interrupted by user.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()