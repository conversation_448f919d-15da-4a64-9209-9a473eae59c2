#!/usr/bin/env python3
"""
Example script demonstrating court RSS monitoring with SQLite database storage.

This script shows how to use the updated courtrss package to monitor court RSS feeds
and save all entries to a local SQLite database.
"""

import subprocess
import sys
import time
import os

def run_court_monitoring_example():
    """Run a simple example of court monitoring."""
    
    print("=== Court RSS Monitoring Example ===")
    print()
    
    # Example 1: Monitor a few courts for a short time
    print("1. Starting court monitoring for a few courts (5 minutes)...")
    print("   This will save all court entries to '.sqlite/court_entries.db'")
    print()
    
    # Use all courts
    test_courts = ["almd", "alsd", "akd", "ared", "arwd",
                   "cacd", "cand", "casd", "ctd", "ded", "dcd",
                   "flmd", "flsd", "gamd", "gud", "idd", "ilcd",
                   "ilnd", "innd", "iand", "iasd", "ksd", "kywd",
                   "laed", "lamd", "lawd", "mad", "mied", "miwd",
                   "moed", "mowd", "mtd", "ned", "nhd", "njd",
                   "nyed", "nynd", "nysd", "nced", "ncmd", "ncwd",
                   "nmid", "ohnd", "ohsd", "okwd", "paed", "pawd",
                   "prd", "rid", "sdd", "tned", "tnmd", "txed",
                   "txsd", "txwd", "utd", "vtd", "vid", "waed",
                   "wvnd", "wvsd", "wied", "wiwd", "wyd"]
    court_codes_str = ",".join(test_courts)
    
    try:
        # Start monitoring in the background for 5 minutes
        print(f"Monitoring courts: {test_courts}")
        print("Database: .sqlite/court_entries.db")
        print("Interval: 30 seconds")
        print()
        
        # Run the monitoring command
        cmd = [
            sys.executable, "-m", "courtrss.rss_feed",
            "--court_mode",
            "--court_codes", court_codes_str,
            "--interval", "30",
            "--retries", "2"
        ]
        
        print("Starting monitoring... (Press Ctrl+C to stop)")
        print("Command:", " ".join(cmd))
        print()
        
        # Run for a limited time in this example
        process = subprocess.Popen(cmd)
        
        # Let it run for a bit, then stop
        time.sleep(60)  # Run for 1 minute in example
        process.terminate()
        process.wait()
        
    except KeyboardInterrupt:
        print("\nStopping monitoring...")
        if 'process' in locals():
            process.terminate()
            process.wait()
    
    # Example 2: Query the database
    print("\n2. Querying the database...")
    
    db_path = os.path.join(".sqlite", "court_entries.db")
    if os.path.exists(db_path):
        # Show database statistics
        print("\nDatabase Statistics:")
        subprocess.run([
            sys.executable, "-m", "courtrss.rss_feed",
            "--db_stats"
        ])
        
        # Show recent entries
        print("\nRecent Entries:")
        subprocess.run([
            sys.executable, "-m", "courtrss.rss_feed",
            "--query_db",
            "--query_limit", "5"
        ])
        
    else:
        print("No database file found. The monitoring may not have had time to collect data.")
    
    print("\n=== Example Complete ===")
    print()
    print("To run continuous monitoring for all courts:")
    print("  python -m courtrss.rss_feed --court_mode")
    print()
    print("To query the database:")
    print("  python -m courtrss.rss_feed --db_stats")
    print("  python -m courtrss.rss_feed --query_db --query_limit 20")
    print("  python -m courtrss.rss_feed --query_court almd")

if __name__ == "__main__":
    run_court_monitoring_example()